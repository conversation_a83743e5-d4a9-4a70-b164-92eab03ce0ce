# TSC Registration Status Automation

This project provides automated tools to check teacher registration status from the Teachers Service Commission (TSC) of Kenya online portal.

## Features

- ✅ Automated form submission to TSC portal
- ✅ Single ID/Passport number checking
- ✅ Batch processing of multiple IDs
- ✅ Multiple output formats (JSON, CSV)
- ✅ Error handling and retry logic
- ✅ Rate limiting to be respectful to the server
- ✅ Detailed logging and reporting
- ✅ Command-line interface

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

### Check a Single ID

```python
from tsc_automation import TSCAutomation

automation = TSCAutomation()
result = automation.check_registration_status("12345678")

print(f"Name: {result.name}")
print(f"Status: {result.registration_status}")
print(f"TSC Number: {result.tsc_number}")
```

### Command Line Usage

**Single ID check:**
```bash
python tsc_api_client.py --id 12345678
```

**Batch processing from file:**
```bash
python tsc_api_client.py --file id_numbers.txt --format csv
```

## File Structure

```
├── tsc_automation.py      # Core automation logic
├── tsc_api_client.py      # Advanced API client with CLI
├── example_usage.py       # Usage examples
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── output/               # Results directory (created automatically)
```

## Usage Examples

### 1. Basic Single Check

```python
from tsc_automation import TSCAutomation

automation = TSCAutomation()
result = automation.check_registration_status("12345678")

if result.success:
    print(f"Teacher: {result.name}")
    print(f"Status: {result.registration_status}")
else:
    print(f"Error: {result.error_message}")
```

### 2. Batch Processing

```python
from tsc_automation import TSCAutomation

automation = TSCAutomation()
id_numbers = ["12345678", "87654321", "11111111"]

results = automation.batch_check(id_numbers, delay=2.0)

for id_num, status in results.items():
    print(f"{id_num}: {status.registration_status}")
```

### 3. Using the Advanced API Client

```python
from tsc_api_client import TSCAPIClient

client = TSCAPIClient(output_dir="my_results")

# Check single ID and save result
result = client.check_single_id("12345678", save_result=True)

# Process IDs from file
results = client.check_from_file("id_list.txt", output_format="both")

# Generate summary report
report = client.generate_report(results)
print(f"Success rate: {report['summary']['success_rate']}")
```

## Input File Format

For batch processing, create a text file with one ID/Passport number per line:

```
12345678
87654321
11111111
22222222
```

## Output Formats

### JSON Output
```json
{
  "timestamp": "2025-01-02T10:30:00",
  "total_checked": 3,
  "results": {
    "12345678": {
      "success": true,
      "name": "JOHN DOE SMITH",
      "registration_status": "Registered TSC NO: 123456",
      "tsc_number": "123456",
      "error_message": null
    }
  }
}
```

### CSV Output
```csv
ID_Number,Success,Name,Registration_Status,TSC_Number,Error_Message
12345678,True,JOHN DOE SMITH,Registered TSC NO: 123456,123456,
87654321,True,,Not Found,,
```

## Command Line Options

```bash
python tsc_api_client.py [OPTIONS]

Options:
  --id ID_NUMBER        Check a single ID number
  --file FILE_PATH      Process IDs from file (one per line)
  --format FORMAT       Output format: json, csv, or both (default: json)
  --output-dir DIR      Output directory for results (default: output)
```

## Data Structure

The `RegistrationStatus` class contains:

- `name`: Teacher's full name
- `registration_status`: Registration status text
- `tsc_number`: TSC registration number
- `employee_status`: Employment status
- `date_checked`: Date of the check
- `success`: Whether the query was successful
- `error_message`: Error details if any
- `raw_response`: Raw HTML response for debugging

## Error Handling

The automation handles various scenarios:

- ✅ Network connectivity issues
- ✅ Server timeouts
- ✅ Invalid ID numbers
- ✅ Rate limiting
- ✅ Parsing errors
- ✅ CSRF token handling

## Rate Limiting

The tool includes built-in delays between requests to be respectful to the TSC server:

- Default delay: 1 second between requests
- Configurable delay in batch operations
- Session management to reduce server load

## Important Notes

1. **Respect the Server**: Don't make too many rapid requests
2. **Test Responsibly**: Use real ID numbers only for legitimate purposes
3. **Data Privacy**: Handle personal information responsibly
4. **Terms of Service**: Ensure compliance with TSC's terms of use

## Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check internet connection
   - Verify TSC portal is accessible
   - Try increasing delay between requests

2. **Parsing Errors**
   - The TSC portal may have changed its format
   - Check the raw_response field for debugging
   - Update parsing logic if needed

3. **No Results Found**
   - Verify ID number format
   - Check if the ID is registered with TSC
   - Ensure ID number is entered correctly

### Debug Mode

Enable detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

To improve the automation:

1. Test with various ID formats
2. Update parsing logic for new portal changes
3. Add additional error handling
4. Improve data extraction accuracy

## Disclaimer

This tool is for educational and legitimate verification purposes only. Users are responsible for:

- Complying with TSC terms of service
- Respecting data privacy laws
- Using the tool responsibly
- Not overloading the TSC servers

## License

This project is provided as-is for educational purposes. Use responsibly and in compliance with applicable laws and terms of service.
