#!/usr/bin/env python3
"""
Advanced TSC API Client with additional features
"""

import json
import csv
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
import argparse
from tsc_automation import TSCAutomation, RegistrationStatus


class TSCAPIClient:
    """
    Advanced API client for TSC registration status checking with additional features
    """
    
    def __init__(self, output_dir: str = "output"):
        self.automation = TSCAutomation()
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def check_single_id(self, id_number: str, save_result: bool = True) -> RegistrationStatus:
        """
        Check a single ID and optionally save the result
        
        Args:
            id_number (str): ID/passport number to check
            save_result (bool): Whether to save the result to file
            
        Returns:
            RegistrationStatus: The registration status result
        """
        result = self.automation.check_registration_status(id_number)
        
        if save_result:
            self._save_single_result(id_number, result)
        
        return result
    
    def check_from_file(self, input_file: str, output_format: str = "json") -> Dict[str, RegistrationStatus]:
        """
        Check multiple IDs from a file
        
        Args:
            input_file (str): Path to file containing ID numbers (one per line)
            output_format (str): Output format ('json', 'csv', or 'both')
            
        Returns:
            Dict[str, RegistrationStatus]: Results dictionary
        """
        # Read ID numbers from file
        with open(input_file, 'r') as f:
            id_numbers = [line.strip() for line in f if line.strip()]
        
        print(f"Found {len(id_numbers)} ID numbers to check")
        
        # Process all IDs
        results = self.automation.batch_check(id_numbers)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if output_format in ["json", "both"]:
            self._save_json_results(results, f"batch_results_{timestamp}.json")
        
        if output_format in ["csv", "both"]:
            self._save_csv_results(results, f"batch_results_{timestamp}.csv")
        
        return results
    
    def _save_single_result(self, id_number: str, result: RegistrationStatus):
        """Save a single result to JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"result_{id_number}_{timestamp}.json"
        
        result_dict = {
            "id_number": id_number,
            "timestamp": timestamp,
            "success": result.success,
            "name": result.name,
            "registration_status": result.registration_status,
            "tsc_number": result.tsc_number,
            "employee_status": result.employee_status,
            "date_checked": result.date_checked,
            "error_message": result.error_message
        }
        
        output_path = self.output_dir / filename
        with open(output_path, 'w') as f:
            json.dump(result_dict, f, indent=2)
        
        print(f"Result saved to: {output_path}")
    
    def _save_json_results(self, results: Dict[str, RegistrationStatus], filename: str):
        """Save batch results to JSON file"""
        output_data = {
            "timestamp": datetime.now().isoformat(),
            "total_checked": len(results),
            "results": {}
        }
        
        for id_number, result in results.items():
            output_data["results"][id_number] = {
                "success": result.success,
                "name": result.name,
                "registration_status": result.registration_status,
                "tsc_number": result.tsc_number,
                "employee_status": result.employee_status,
                "date_checked": result.date_checked,
                "error_message": result.error_message
            }
        
        output_path = self.output_dir / filename
        with open(output_path, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"JSON results saved to: {output_path}")
    
    def _save_csv_results(self, results: Dict[str, RegistrationStatus], filename: str):
        """Save batch results to CSV file"""
        output_path = self.output_dir / filename
        
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow([
                'ID_Number',
                'Success',
                'Name',
                'Registration_Status',
                'TSC_Number',
                'Employee_Status',
                'Date_Checked',
                'Error_Message'
            ])
            
            # Write data
            for id_number, result in results.items():
                writer.writerow([
                    id_number,
                    result.success,
                    result.name or '',
                    result.registration_status or '',
                    result.tsc_number or '',
                    result.employee_status or '',
                    result.date_checked or '',
                    result.error_message or ''
                ])
        
        print(f"CSV results saved to: {output_path}")
    
    def generate_report(self, results: Dict[str, RegistrationStatus]) -> Dict:
        """Generate a summary report of the results"""
        total = len(results)
        successful = sum(1 for r in results.values() if r.success)
        registered = sum(1 for r in results.values() if r.registration_status and 'registered' in r.registration_status.lower())
        errors = sum(1 for r in results.values() if r.error_message)
        
        report = {
            "summary": {
                "total_checked": total,
                "successful_queries": successful,
                "registered_teachers": registered,
                "errors": errors,
                "success_rate": f"{(successful/total)*100:.1f}%" if total > 0 else "0%"
            },
            "status_breakdown": {}
        }
        
        # Count different status types
        status_counts = {}
        for result in results.values():
            status = result.registration_status or "Unknown"
            status_counts[status] = status_counts.get(status, 0) + 1
        
        report["status_breakdown"] = status_counts
        
        return report


def main():
    """Command line interface for the TSC API client"""
    parser = argparse.ArgumentParser(description="TSC Registration Status Checker")
    parser.add_argument("--id", help="Single ID number to check")
    parser.add_argument("--file", help="File containing ID numbers (one per line)")
    parser.add_argument("--format", choices=["json", "csv", "both"], default="json",
                       help="Output format for batch results")
    parser.add_argument("--output-dir", default="output", help="Output directory for results")
    
    args = parser.parse_args()
    
    client = TSCAPIClient(output_dir=args.output_dir)
    
    if args.id:
        # Check single ID
        print(f"Checking ID: {args.id}")
        result = client.check_single_id(args.id)
        
        print("\n" + "="*50)
        print("RESULT")
        print("="*50)
        print(f"Success: {result.success}")
        print(f"Name: {result.name}")
        print(f"Status: {result.registration_status}")
        print(f"TSC Number: {result.tsc_number}")
        if result.error_message:
            print(f"Error: {result.error_message}")
    
    elif args.file:
        # Check from file
        print(f"Processing file: {args.file}")
        results = client.check_from_file(args.file, args.format)
        
        # Generate and display report
        report = client.generate_report(results)
        print("\n" + "="*50)
        print("SUMMARY REPORT")
        print("="*50)
        for key, value in report["summary"].items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        
        print("\nStatus Breakdown:")
        for status, count in report["status_breakdown"].items():
            print(f"  {status}: {count}")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
