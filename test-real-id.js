#!/usr/bin/env node

/**
 * Test script for checking real TSC ID numbers with detailed debugging
 */

import * as cheerio from 'cheerio';
import { TSCAutomation } from './tsc-automation.js';

/**
 * Test an ID number with detailed debugging output
 */
async function testIdWithDebug(idNumber) {
    console.log(`Testing ID: ${idNumber}`);
    console.log("=".repeat(50));
    
    const automation = new TSCAutomation();
    const result = await automation.checkRegistrationStatus(idNumber);
    
    console.log(`Success: ${result.success}`);
    console.log(`Name: ${result.name}`);
    console.log(`Registration Status: ${result.registrationStatus}`);
    console.log(`TSC Number: ${result.tscNumber}`);
    console.log(`Date Checked: ${result.dateChecked}`);
    
    if (result.errorMessage) {
        console.log(`Error: ${result.errorMessage}`);
    }
    
    if (result.rawResponse) {
        console.log(`\nRaw response length: ${result.rawResponse.length}`);
        
        // Parse and show the main content
        const $ = cheerio.load(result.rawResponse);
        
        // Look for any alert or message boxes
        const alerts = $('.alert, .message, .error, .success');
        if (alerts.length > 0) {
            console.log("\nAlert/Message boxes found:");
            alerts.each((i, element) => {
                console.log(`  - ${$(element).text().trim()}`);
            });
        }
        
        // Show the main body text (cleaned up)
        const body = $('body');
        if (body.length > 0) {
            const textContent = body.text();
            // Clean up the text
            const lines = textContent
                .split('\n')
                .map(line => line.trim())
                .filter(line => line);
            
            // Filter out common navigation/header text
            const skipWords = [
                'teachersonline', 'teachers service commission', 'online services', 
                'check registration status', 'enter id', 'search', 'print', 
                'reset remarks', 'all rights reserved'
            ];
            
            const filteredLines = lines.filter(line => {
                return !skipWords.some(skipWord => line.toLowerCase().includes(skipWord));
            });
            
            console.log(`\nMain content (filtered):`);
            filteredLines.slice(0, 10).forEach(line => {
                console.log(`  ${line}`);
            });
            
            if (filteredLines.length > 10) {
                console.log(`  ... and ${filteredLines.length - 10} more lines`);
            }
        }
    }
    
    console.log("\n" + "=".repeat(50));
}

/**
 * Main function to test ID numbers
 */
async function main() {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        // Test ID provided as command line argument
        const testId = args[0];
        await testIdWithDebug(testId);
    } else {
        // Test some sample IDs
        const testIds = [
            "11209837",  // Known working ID
            "373774",    // From screenshot
            "12345678",  // Test ID
            "100000",    // Short ID
        ];
        
        for (const testId of testIds) {
            await testIdWithDebug(testId);
            console.log("\n");
        }
    }
}

// Run if this file is executed directly
if (process.argv[1] && import.meta.url.includes(process.argv[1].replace(/\\/g, '/'))) {
    main().catch(console.error);
}
