#!/usr/bin/env python3
"""
Test script for checking real TSC ID numbers
"""

from tsc_automation import TSCAutomation
from bs4 import BeautifulSoup
import sys

def test_id_with_debug(id_number):
    """Test an ID number with detailed debugging output"""
    print(f"Testing ID: {id_number}")
    print("="*50)
    
    automation = TSCAutomation()
    result = automation.check_registration_status(id_number)
    
    print(f"Success: {result.success}")
    print(f"Name: {result.name}")
    print(f"Registration Status: {result.registration_status}")
    print(f"TSC Number: {result.tsc_number}")
    print(f"Date Checked: {result.date_checked}")
    
    if result.error_message:
        print(f"Error: {result.error_message}")
    
    if result.raw_response:
        print(f"\nRaw response length: {len(result.raw_response)}")
        
        # Parse and show the main content
        soup = BeautifulSoup(result.raw_response, 'html.parser')
        
        # Look for any alert or message boxes
        alerts = soup.find_all(['div'], class_=['alert', 'message', 'error', 'success'])
        if alerts:
            print("\nAlert/Message boxes found:")
            for alert in alerts:
                print(f"  - {alert.get_text().strip()}")
        
        # Show the main body text (cleaned up)
        body = soup.find('body')
        if body:
            text_content = body.get_text()
            # Clean up the text
            lines = [line.strip() for line in text_content.split('\n') if line.strip()]
            # Filter out common navigation/header text
            filtered_lines = []
            skip_words = ['teachersonline', 'teachers service commission', 'online services', 
                         'check registration status', 'enter id', 'search', 'print', 
                         'reset remarks', 'all rights reserved']
            
            for line in lines:
                if not any(skip_word in line.lower() for skip_word in skip_words):
                    filtered_lines.append(line)
            
            print(f"\nMain content (filtered):")
            for line in filtered_lines[:10]:  # Show first 10 relevant lines
                print(f"  {line}")
            
            if len(filtered_lines) > 10:
                print(f"  ... and {len(filtered_lines) - 10} more lines")
    
    print("\n" + "="*50)

def main():
    """Main function to test ID numbers"""
    if len(sys.argv) > 1:
        # Test ID provided as command line argument
        test_id = sys.argv[1]
        test_id_with_debug(test_id)
    else:
        # Test some sample IDs
        test_ids = [
            "373774",  # From your screenshot
            "12345678",  # Test ID
            "100000",   # Short ID
            "1000000",  # Different length
        ]
        
        for test_id in test_ids:
            test_id_with_debug(test_id)
            print("\n")

if __name__ == "__main__":
    main()
