#!/usr/bin/env python3
"""
TSC (Teachers Service Commission of Kenya) Registration Status Automation

This module provides functionality to automate the checking of teacher registration
status from the TSC online portal.
"""

import requests
from bs4 import BeautifulSoup
import re
import time
from typing import Dict, Optional, Union
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class RegistrationStatus:
    """Data class to hold registration status information"""
    name: Optional[str] = None
    registration_status: Optional[str] = None
    tsc_number: Optional[str] = None
    employee_status: Optional[str] = None
    date_checked: Optional[str] = None
    raw_response: Optional[str] = None
    success: bool = False
    error_message: Optional[str] = None


class TSCAutomation:
    """
    Automates TSC registration status checking
    """
    
    def __init__(self):
        self.base_url = "https://tsconline.tsc.go.ke"
        self.status_url = f"{self.base_url}/register/registration-status"
        self.session = requests.Session()
        
        # Set headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': self.status_url,
        })
    
    def check_registration_status(self, id_number: str) -> RegistrationStatus:
        """
        Check registration status for a given ID/Passport number
        
        Args:
            id_number (str): The ID or passport number to check
            
        Returns:
            RegistrationStatus: Object containing the registration information
        """
        try:
            logger.info(f"Checking registration status for ID: {id_number}")
            
            # First, get the initial page to establish session
            initial_response = self.session.get(self.status_url)
            initial_response.raise_for_status()
            
            # Parse the initial page to get any required form tokens or CSRF tokens
            soup = BeautifulSoup(initial_response.content, 'html.parser')
            
            # Prepare form data with correct field name
            form_data = {'id_no': id_number}

            # Extract CSRF token
            csrf_input = soup.find('input', {'name': '_csrf'})
            if csrf_input and csrf_input.get('value'):
                form_data['_csrf'] = csrf_input.get('value')
                logger.info("Found CSRF token")
            else:
                logger.warning("No CSRF token found")

            # Check for any other hidden fields
            hidden_inputs = soup.find_all('input', type='hidden')
            for hidden_input in hidden_inputs:
                name = hidden_input.get('name')
                value = hidden_input.get('value')
                if name and value and name not in form_data:
                    form_data[name] = value
            
            # Submit the search request
            logger.info(f"Submitting form data: {form_data}")
            search_response = self.session.post(
                self.status_url,
                data=form_data,
                allow_redirects=True
            )
            logger.info(f"Response status: {search_response.status_code}")
            logger.info(f"Response URL: {search_response.url}")
            logger.info(f"Response content length: {len(search_response.content)}")
            search_response.raise_for_status()
            
            # Parse the response
            return self._parse_response(search_response.text, id_number)
            
        except requests.RequestException as e:
            logger.error(f"Request failed: {str(e)}")
            return RegistrationStatus(
                success=False,
                error_message=f"Request failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return RegistrationStatus(
                success=False,
                error_message=f"Unexpected error: {str(e)}"
            )
    
    def _parse_response(self, html_content: str, id_number: str) -> RegistrationStatus:
        """
        Parse the HTML response to extract registration information
        
        Args:
            html_content (str): The HTML content from the response
            id_number (str): The ID number that was searched
            
        Returns:
            RegistrationStatus: Parsed registration information
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Initialize result
        result = RegistrationStatus(raw_response=html_content)
        
        # Look for the main content area
        main_content = soup.find('div', class_='container') or soup.find('body')
        
        if not main_content:
            result.error_message = "Could not find main content in response"
            return result
        
        # Extract text content
        text_content = main_content.get_text()
        
        # Extract name from text content
        lines = text_content.split('\n')
        for line in lines:
            line = line.strip()
            # Look for lines that look like names (all caps, multiple words)
            if (len(line) > 5 and
                line.isupper() and
                ' ' in line and
                not any(word in line.lower() for word in
                    ['registration', 'status', 'commission', 'teachers', 'service', 'kenya', 'date', 'print'])):
                result.name = line
                break
        
        # Look for specific TSC response messages
        if "NOT associated to any registered teacher" in text_content:
            result.registration_status = "Not Registered"
            result.success = True
        elif "Registered TSC NO" in text_content:
            # Extract TSC number and status
            tsc_match = re.search(r'Registered TSC NO[:\s]*(\d+)', text_content, re.IGNORECASE)
            if tsc_match:
                result.tsc_number = tsc_match.group(1)
                result.registration_status = f"Registered TSC NO: {result.tsc_number}"
                result.success = True
        elif "Active TSC Employee" in text_content:
            result.registration_status = "Active TSC Employee"
            result.success = True
        else:
            # Look for other status patterns
            status_patterns = [
                r'Registration Status[:\s]*([^\n]+)',
                r'Status[:\s]*([^\n]+)',
                r'(Registered)',
                r'(Not Registered)'
            ]

            for pattern in status_patterns:
                status_match = re.search(pattern, text_content, re.IGNORECASE)
                if status_match:
                    result.registration_status = status_match.group(1).strip()
                    result.success = True
                    break
        
        # Extract TSC number
        tsc_patterns = [
            r'TSC NO[:\s]*(\d+)',
            r'TSC Number[:\s]*(\d+)',
            r'Registration Number[:\s]*(\d+)'
        ]
        
        for pattern in tsc_patterns:
            tsc_match = re.search(pattern, text_content, re.IGNORECASE)
            if tsc_match:
                result.tsc_number = tsc_match.group(1).strip()
                break
        
        # Extract date if present
        date_patterns = [
            r'Date[:\s]*(\d{2}-\d{2}-\d{4})',
            r'(\d{2}-\d{2}-\d{4})'
        ]
        
        for pattern in date_patterns:
            date_match = re.search(pattern, text_content)
            if date_match:
                result.date_checked = date_match.group(1).strip()
                break
        
        # Check if this looks like a "not found" response
        not_found_indicators = [
            'no record found',
            'not registered',
            'invalid id',
            'record not found'
        ]
        
        if any(indicator in text_content.lower() for indicator in not_found_indicators):
            result.success = True  # Successfully got a response, just no record found
            result.registration_status = "Not Found"
        
        # If we found a name or status, consider it successful
        if result.name or result.registration_status:
            result.success = True
        
        if not result.success and not result.error_message:
            result.error_message = "Could not parse registration information from response"
        
        return result
    
    def batch_check(self, id_numbers: list, delay: float = 1.0) -> Dict[str, RegistrationStatus]:
        """
        Check multiple ID numbers with optional delay between requests
        
        Args:
            id_numbers (list): List of ID/passport numbers to check
            delay (float): Delay in seconds between requests (default: 1.0)
            
        Returns:
            Dict[str, RegistrationStatus]: Dictionary mapping ID numbers to their status
        """
        results = {}
        
        for i, id_number in enumerate(id_numbers):
            logger.info(f"Processing {i+1}/{len(id_numbers)}: {id_number}")
            results[id_number] = self.check_registration_status(id_number)
            
            # Add delay between requests to be respectful to the server
            if i < len(id_numbers) - 1:
                time.sleep(delay)
        
        return results


def main():
    """Example usage of the TSC automation"""
    automation = TSCAutomation()
    
    # Example ID number (replace with actual ID)
    test_id = "12345678"
    
    print(f"Checking registration status for ID: {test_id}")
    result = automation.check_registration_status(test_id)
    
    print("\n" + "="*50)
    print("REGISTRATION STATUS RESULT")
    print("="*50)
    print(f"Success: {result.success}")
    print(f"Name: {result.name}")
    print(f"Registration Status: {result.registration_status}")
    print(f"TSC Number: {result.tsc_number}")
    print(f"Date Checked: {result.date_checked}")
    
    if result.error_message:
        print(f"Error: {result.error_message}")
    
    # Example of batch processing
    # batch_ids = ["12345678", "87654321", "11111111"]
    # batch_results = automation.batch_check(batch_ids)
    # 
    # for id_num, status in batch_results.items():
    #     print(f"{id_num}: {status.registration_status}")


if __name__ == "__main__":
    main()
