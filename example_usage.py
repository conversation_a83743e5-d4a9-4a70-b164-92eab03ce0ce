#!/usr/bin/env python3
"""
Example usage of the TSC automation tools
"""

from tsc_automation import TSCAutomation
from tsc_api_client import TSCAPIClient


def example_single_check():
    """Example of checking a single ID"""
    print("=== Single ID Check Example ===")
    
    automation = TSCAutomation()
    
    # Replace with actual ID number
    test_id = "12345678"
    
    result = automation.check_registration_status(test_id)
    
    print(f"ID: {test_id}")
    print(f"Success: {result.success}")
    print(f"Name: {result.name}")
    print(f"Status: {result.registration_status}")
    print(f"TSC Number: {result.tsc_number}")
    
    if result.error_message:
        print(f"Error: {result.error_message}")


def example_batch_check():
    """Example of checking multiple IDs"""
    print("\n=== Batch Check Example ===")
    
    automation = TSCAutomation()
    
    # Replace with actual ID numbers
    test_ids = ["12345678", "87654321", "11111111"]
    
    results = automation.batch_check(test_ids, delay=2.0)  # 2 second delay between requests
    
    for id_number, result in results.items():
        print(f"\nID: {id_number}")
        print(f"  Success: {result.success}")
        print(f"  Name: {result.name}")
        print(f"  Status: {result.registration_status}")
        if result.error_message:
            print(f"  Error: {result.error_message}")


def example_api_client():
    """Example using the advanced API client"""
    print("\n=== API Client Example ===")
    
    client = TSCAPIClient(output_dir="my_results")
    
    # Check single ID with automatic saving
    test_id = "12345678"
    result = client.check_single_id(test_id, save_result=True)
    
    print(f"Checked ID {test_id}: {result.registration_status}")


def create_sample_id_file():
    """Create a sample file with ID numbers for batch processing"""
    sample_ids = [
        "12345678",
        "87654321", 
        "11111111",
        "22222222",
        "33333333"
    ]
    
    with open("sample_ids.txt", "w") as f:
        for id_num in sample_ids:
            f.write(f"{id_num}\n")
    
    print("Created sample_ids.txt with test ID numbers")


if __name__ == "__main__":
    # Run examples
    example_single_check()
    example_batch_check()
    example_api_client()
    
    # Create sample file for testing
    create_sample_id_file()
    
    print("\n" + "="*60)
    print("USAGE INSTRUCTIONS")
    print("="*60)
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. For single ID check:")
    print("   python tsc_api_client.py --id 12345678")
    print("3. For batch processing:")
    print("   python tsc_api_client.py --file sample_ids.txt --format csv")
    print("4. Results will be saved in the 'output' directory")
    print("\nNote: Replace the example ID numbers with real ones for testing")
