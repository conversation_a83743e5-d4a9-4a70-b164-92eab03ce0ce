#!/usr/bin/env node

/**
 * Advanced TSC API Client with additional features
 */

import { Command } from 'commander';
import createCsvWriter from 'csv-writer';
import fs from 'fs-extra';
import path from 'path';
import { RegistrationStatus, TSCAutomation } from './tsc-automation.js';

/**
 * Advanced API client for TSC registration status checking with additional features
 */
class TSCAPIClient {
    constructor(outputDir = "output") {
        this.automation = new TSCAutomation();
        this.outputDir = outputDir;
        
        // Ensure output directory exists
        fs.ensureDirSync(this.outputDir);
    }

    /**
     * Check a single ID and optionally save the result
     * 
     * @param {string} idNumber - ID/passport number to check
     * @param {boolean} saveResult - Whether to save the result to file
     * @returns {Promise<RegistrationStatus>} The registration status result
     */
    async checkSingleId(idNumber, saveResult = true) {
        const result = await this.automation.checkRegistrationStatus(idNumber);
        
        if (saveResult) {
            await this._saveSingleResult(idNumber, result);
        }
        
        return result;
    }

    /**
     * Check multiple IDs from a file
     * 
     * @param {string} inputFile - Path to file containing ID numbers (one per line)
     * @param {string} outputFormat - Output format ('json', 'csv', or 'both')
     * @returns {Promise<Object>} Results dictionary
     */
    async checkFromFile(inputFile, outputFormat = "json") {
        // Read ID numbers from file
        const fileContent = await fs.readFile(inputFile, 'utf8');
        const idNumbers = fileContent
            .split('\n')
            .map(line => line.trim())
            .filter(line => line);
        
        console.log(`Found ${idNumbers.length} ID numbers to check`);
        
        // Process all IDs
        const results = await this.automation.batchCheck(idNumbers);
        
        // Save results
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        
        if (outputFormat === "json" || outputFormat === "both") {
            await this._saveJsonResults(results, `batch_results_${timestamp}.json`);
        }
        
        if (outputFormat === "csv" || outputFormat === "both") {
            await this._saveCsvResults(results, `batch_results_${timestamp}.csv`);
        }
        
        return results;
    }

    /**
     * Save a single result to JSON file
     */
    async _saveSingleResult(idNumber, result) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const filename = `result_${idNumber}_${timestamp}.json`;
        
        const resultDict = {
            id_number: idNumber,
            timestamp: timestamp,
            success: result.success,
            name: result.name,
            registration_status: result.registrationStatus,
            tsc_number: result.tscNumber,
            employee_status: result.employeeStatus,
            date_checked: result.dateChecked,
            error_message: result.errorMessage
        };
        
        const outputPath = path.join(this.outputDir, filename);
        await fs.writeJson(outputPath, resultDict, { spaces: 2 });
        
        console.log(`Result saved to: ${outputPath}`);
    }

    /**
     * Save batch results to JSON file
     */
    async _saveJsonResults(results, filename) {
        const outputData = {
            timestamp: new Date().toISOString(),
            total_checked: Object.keys(results).length,
            results: {}
        };
        
        for (const [idNumber, result] of Object.entries(results)) {
            outputData.results[idNumber] = {
                success: result.success,
                name: result.name,
                registration_status: result.registrationStatus,
                tsc_number: result.tscNumber,
                employee_status: result.employeeStatus,
                date_checked: result.dateChecked,
                error_message: result.errorMessage
            };
        }
        
        const outputPath = path.join(this.outputDir, filename);
        await fs.writeJson(outputPath, outputData, { spaces: 2 });
        
        console.log(`JSON results saved to: ${outputPath}`);
    }

    /**
     * Save batch results to CSV file
     */
    async _saveCsvResults(results, filename) {
        const outputPath = path.join(this.outputDir, filename);
        
        const csvWriter = createCsvWriter.createObjectCsvWriter({
            path: outputPath,
            header: [
                { id: 'id_number', title: 'ID_Number' },
                { id: 'success', title: 'Success' },
                { id: 'name', title: 'Name' },
                { id: 'registration_status', title: 'Registration_Status' },
                { id: 'tsc_number', title: 'TSC_Number' },
                { id: 'employee_status', title: 'Employee_Status' },
                { id: 'date_checked', title: 'Date_Checked' },
                { id: 'error_message', title: 'Error_Message' }
            ]
        });
        
        const records = [];
        for (const [idNumber, result] of Object.entries(results)) {
            records.push({
                id_number: idNumber,
                success: result.success,
                name: result.name || '',
                registration_status: result.registrationStatus || '',
                tsc_number: result.tscNumber || '',
                employee_status: result.employeeStatus || '',
                date_checked: result.dateChecked || '',
                error_message: result.errorMessage || ''
            });
        }
        
        await csvWriter.writeRecords(records);
        console.log(`CSV results saved to: ${outputPath}`);
    }

    /**
     * Generate a summary report of the results
     */
    generateReport(results) {
        const total = Object.keys(results).length;
        const successful = Object.values(results).filter(r => r.success).length;
        const registered = Object.values(results).filter(r => 
            r.registrationStatus && r.registrationStatus.toLowerCase().includes('registered')
        ).length;
        const errors = Object.values(results).filter(r => r.errorMessage).length;
        
        const report = {
            summary: {
                total_checked: total,
                successful_queries: successful,
                registered_teachers: registered,
                errors: errors,
                success_rate: total > 0 ? `${((successful / total) * 100).toFixed(1)}%` : "0%"
            },
            status_breakdown: {}
        };
        
        // Count different status types
        const statusCounts = {};
        for (const result of Object.values(results)) {
            const status = result.registrationStatus || "Unknown";
            statusCounts[status] = (statusCounts[status] || 0) + 1;
        }
        
        report.status_breakdown = statusCounts;
        
        return report;
    }
}

/**
 * Command line interface for the TSC API client
 */
async function main() {
    const program = new Command();
    
    program
        .name('tsc-api-client')
        .description('TSC Registration Status Checker')
        .version('1.0.0');
    
    program
        .option('--id <id>', 'Single ID number to check')
        .option('--file <file>', 'File containing ID numbers (one per line)')
        .option('--format <format>', 'Output format for batch results', 'json')
        .option('--output-dir <dir>', 'Output directory for results', 'output');
    
    program.parse();
    
    const options = program.opts();
    
    const client = new TSCAPIClient(options.outputDir);
    
    if (options.id) {
        // Check single ID
        console.log(`Checking ID: ${options.id}`);
        const result = await client.checkSingleId(options.id);
        
        console.log("\n" + "=".repeat(50));
        console.log("RESULT");
        console.log("=".repeat(50));
        console.log(`Success: ${result.success}`);
        console.log(`Name: ${result.name}`);
        console.log(`Status: ${result.registrationStatus}`);
        console.log(`TSC Number: ${result.tscNumber}`);
        if (result.errorMessage) {
            console.log(`Error: ${result.errorMessage}`);
        }
    } else if (options.file) {
        // Check from file
        console.log(`Processing file: ${options.file}`);
        const results = await client.checkFromFile(options.file, options.format);
        
        // Generate and display report
        const report = client.generateReport(results);
        console.log("\n" + "=".repeat(50));
        console.log("SUMMARY REPORT");
        console.log("=".repeat(50));
        for (const [key, value] of Object.entries(report.summary)) {
            console.log(`${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}: ${value}`);
        }
        
        console.log("\nStatus Breakdown:");
        for (const [status, count] of Object.entries(report.status_breakdown)) {
            console.log(`  ${status}: ${count}`);
        }
    } else {
        program.help();
    }
}

// Run the CLI if this file is executed directly
if (process.argv[1] && import.meta.url.includes(process.argv[1].replace(/\\/g, '/'))) {
    main().catch(console.error);
}

export { TSCAPIClient };

