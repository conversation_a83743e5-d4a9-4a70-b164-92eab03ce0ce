#!/usr/bin/env node

/**
 * Example usage of the TSC automation tools in JavaScript
 */

import fs from 'fs-extra';
import { TSCAPIClient } from './tsc-api-client.js';
import { TSCAutomation } from './tsc-automation.js';

/**
 * Example of checking a single ID
 */
async function exampleSingleCheck() {
    console.log("=== Single ID Check Example ===");
    
    const automation = new TSCAutomation();
    
    // Replace with actual ID number
    const testId = "11209837";
    
    const result = await automation.checkRegistrationStatus(testId);
    
    console.log(`ID: ${testId}`);
    console.log(`Success: ${result.success}`);
    console.log(`Name: ${result.name}`);
    console.log(`Status: ${result.registrationStatus}`);
    console.log(`TSC Number: ${result.tscNumber}`);
    
    if (result.errorMessage) {
        console.log(`Error: ${result.errorMessage}`);
    }
}

/**
 * Example of checking multiple IDs
 */
async function exampleBatchCheck() {
    console.log("\n=== Batch Check Example ===");
    
    const automation = new TSCAutomation();
    
    // Replace with actual ID numbers
    const testIds = ["11209837", "12345678", "87654321"];
    
    const results = await automation.batchCheck(testIds, 2.0); // 2 second delay between requests
    
    for (const [idNumber, result] of Object.entries(results)) {
        console.log(`\nID: ${idNumber}`);
        console.log(`  Success: ${result.success}`);
        console.log(`  Name: ${result.name}`);
        console.log(`  Status: ${result.registrationStatus}`);
        if (result.errorMessage) {
            console.log(`  Error: ${result.errorMessage}`);
        }
    }
}

/**
 * Example using the advanced API client
 */
async function exampleApiClient() {
    console.log("\n=== API Client Example ===");
    
    const client = new TSCAPIClient("my_results");
    
    // Check single ID with automatic saving
    const testId = "11209837";
    const result = await client.checkSingleId(testId, true);
    
    console.log(`Checked ID ${testId}: ${result.registrationStatus}`);
}

/**
 * Create a sample file with ID numbers for batch processing
 */
async function createSampleIdFile() {
    const sampleIds = [
        "11209837",
        "12345678", 
        "87654321",
        "373774",
        "11111111"
    ];
    
    await fs.writeFile("sample_ids.txt", sampleIds.join('\n'));
    console.log("Created sample_ids.txt with test ID numbers");
}

/**
 * Main function to run all examples
 */
async function main() {
    try {
        // Run examples
        await exampleSingleCheck();
        await exampleBatchCheck();
        await exampleApiClient();
        
        // Create sample file for testing
        await createSampleIdFile();
        
        console.log("\n" + "=".repeat(60));
        console.log("USAGE INSTRUCTIONS");
        console.log("=".repeat(60));
        console.log("1. Install dependencies: npm install");
        console.log("2. For single ID check:");
        console.log("   node tsc-api-client.js --id 11209837");
        console.log("3. For batch processing:");
        console.log("   node tsc-api-client.js --file sample_ids.txt --format csv");
        console.log("4. Results will be saved in the 'output' directory");
        console.log("\nNote: Replace the example ID numbers with real ones for testing");
        
    } catch (error) {
        console.error(`Error: ${error.message}`);
    }
}

// Run if this file is executed directly
if (process.argv[1] && import.meta.url.includes(process.argv[1].replace(/\\/g, '/'))) {
    main();
}
